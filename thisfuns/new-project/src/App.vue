<template>
  <div id="app">
    <!-- 导航栏 -->
    <NavBar />

    <!-- 主要内容区域 -->
    <main class="main-content">
      <router-view />
    </main>

    <!-- 页脚 -->
    <FooterSection />
  </div>
</template>

<script>
import NavBar from '@/components/NavBar.vue'
import FooterSection from '@/components/FooterSection.vue'

export default {
  name: 'App',
  components: {
    NavBar,
    FooterSection
  }
}
</script>

<style lang="scss">
// 导入全局样式
@import '@/styles/main.scss';

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 0.7rem; // 为固定导航栏留出空间
}

// 移动端适配
@media (max-width: 768px) {
  .main-content {
    padding-top: 0.6rem;
  }
}
</style>
