<template>
  <div class="about">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">关于我们</h1>
        <p class="page-subtitle">了解Thisfun的故事与使命</p>
      </div>
    </section>
    
    <!-- 公司介绍 -->
    <section class="company-intro">
      <div class="container">
        <div class="intro-content">
          <div class="intro-text">
            <h2>我们的使命</h2>
            <p>
              Thisfun致力于为全球玩家创造最优质的游戏体验。我们相信游戏不仅仅是娱乐，
              更是连接人与人之间的桥梁，是激发创造力和想象力的源泉。
            </p>
            <p>
              自成立以来，我们始终坚持以玩家为中心的理念，不断探索和创新，
              为玩家提供多样化、高品质的游戏内容。
            </p>
          </div>
          <div class="intro-image">
            <img src="@/assets/images/company.png" alt="公司介绍" />
          </div>
        </div>
      </div>
    </section>
    
    <!-- 核心价值 -->
    <section class="core-values">
      <div class="container">
        <h2 class="section-title">核心价值</h2>
        <div class="values-grid">
          <div class="value-item">
            <div class="value-icon">🎮</div>
            <h3>创新游戏</h3>
            <p>持续推出创新性游戏内容，为玩家带来全新体验</p>
          </div>
          <div class="value-item">
            <div class="value-icon">🌟</div>
            <h3>品质保证</h3>
            <p>严格把控游戏品质，确保每一款游戏都达到最高标准</p>
          </div>
          <div class="value-item">
            <div class="value-icon">🤝</div>
            <h3>社区共建</h3>
            <p>与玩家共同建设游戏社区，倾听每一个声音</p>
          </div>
          <div class="value-item">
            <div class="value-icon">🚀</div>
            <h3>技术领先</h3>
            <p>运用最新技术，为玩家提供流畅稳定的游戏环境</p>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 团队介绍 -->
    <section class="team-section">
      <div class="container">
        <h2 class="section-title">我们的团队</h2>
        <p class="section-description">
          我们拥有一支充满激情和创造力的团队，每个成员都是游戏行业的专家，
          致力于为玩家创造最佳的游戏体验。
        </p>
        
        <div class="team-stats">
          <div class="stat-item">
            <div class="stat-number">50+</div>
            <div class="stat-label">团队成员</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">100+</div>
            <div class="stat-label">游戏作品</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">1M+</div>
            <div class="stat-label">活跃用户</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">5</div>
            <div class="stat-label">年经验</div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 联系我们 -->
    <section class="contact-cta">
      <div class="container">
        <div class="cta-content">
          <h2>想要了解更多？</h2>
          <p>欢迎联系我们，我们很乐意与您分享更多关于Thisfun的故事</p>
          <router-link to="/contact" class="btn btn-primary btn-large">
            联系我们
          </router-link>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: 'AboutView'
}
</script>

<style lang="scss" scoped>
.about {
  .page-header {
    background: linear-gradient(135deg, var(--primary-color), darken(#e57c58, 20%));
    color: white;
    padding: 1rem 0 0.8rem;
    text-align: center;
    
    .page-title {
      font-size: 0.4rem;
      font-weight: 700;
      margin-bottom: 0.1rem;
    }
    
    .page-subtitle {
      font-size: 0.16rem;
      opacity: 0.9;
      margin: 0;
    }
  }
  
  .company-intro {
    padding: 0.8rem 0;
    background: white;
    
    .intro-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0.6rem;
      align-items: center;
      
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 0.4rem;
      }
      
      .intro-text {
        h2 {
          font-size: 0.28rem;
          font-weight: 700;
          color: #333;
          margin-bottom: 0.2rem;
        }
        
        p {
          font-size: 0.14rem;
          line-height: 1.8;
          color: #666;
          margin-bottom: 0.2rem;
        }
      }
      
      .intro-image {
        text-align: center;
        
        img {
          max-width: 100%;
          height: auto;
          border-radius: 0.08rem;
        }
      }
    }
  }
  
  .core-values {
    padding: 0.8rem 0;
    background: #f8f9fa;
    
    .section-title {
      text-align: center;
      font-size: 0.32rem;
      font-weight: 700;
      color: #333;
      margin-bottom: 0.6rem;
    }
    
    .values-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(2.5rem, 1fr));
      gap: 0.4rem;
      
      .value-item {
        background: white;
        padding: 0.4rem;
        border-radius: 0.08rem;
        text-align: center;
        box-shadow: 0 0.02rem 0.08rem rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
        
        &:hover {
          transform: translateY(-0.04rem);
        }
        
        .value-icon {
          font-size: 0.4rem;
          margin-bottom: 0.2rem;
        }
        
        h3 {
          font-size: 0.18rem;
          font-weight: 600;
          color: #333;
          margin-bottom: 0.1rem;
        }
        
        p {
          font-size: 0.12rem;
          color: #666;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }
  
  .team-section {
    padding: 0.8rem 0;
    background: white;
    text-align: center;
    
    .section-title {
      font-size: 0.32rem;
      font-weight: 700;
      color: #333;
      margin-bottom: 0.2rem;
    }
    
    .section-description {
      font-size: 0.14rem;
      color: #666;
      line-height: 1.8;
      max-width: 8rem;
      margin: 0 auto 0.6rem;
    }
    
    .team-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(2rem, 1fr));
      gap: 0.4rem;
      max-width: 10rem;
      margin: 0 auto;
      
      .stat-item {
        .stat-number {
          font-size: 0.36rem;
          font-weight: 700;
          color: var(--primary-color);
          margin-bottom: 0.08rem;
        }
        
        .stat-label {
          font-size: 0.12rem;
          color: #666;
          font-weight: 500;
        }
      }
    }
  }
  
  .contact-cta {
    padding: 0.8rem 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    
    .cta-content {
      text-align: center;
      
      h2 {
        font-size: 0.28rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.15rem;
      }
      
      p {
        font-size: 0.14rem;
        color: #666;
        margin-bottom: 0.3rem;
      }
    }
  }
}
</style>
