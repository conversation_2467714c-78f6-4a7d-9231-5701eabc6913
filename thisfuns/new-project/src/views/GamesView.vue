<template>
  <div class="games">
    <!-- 页面头部 -->
    <section class="page-header">
      <div class="container">
        <h1 class="page-title">游戏中心</h1>
        <p class="page-subtitle">发现你喜爱的游戏</p>
      </div>
    </section>
    
    <!-- 游戏筛选 -->
    <section class="games-filter">
      <div class="container">
        <div class="filter-tabs">
          <button 
            v-for="category in categories" 
            :key="category.id"
            class="filter-tab"
            :class="{ 'active': activeCategory === category.id }"
            @click="setActiveCategory(category.id)"
          >
            {{ category.name }}
          </button>
        </div>
        
        <div class="filter-search">
          <input 
            v-model="searchQuery"
            type="text" 
            placeholder="搜索游戏..."
            class="search-input"
          />
        </div>
      </div>
    </section>
    
    <!-- 游戏列表 -->
    <section class="games-list">
      <div class="container">
        <div class="games-grid">
          <GameCard 
            v-for="game in filteredGames" 
            :key="game.id"
            :game="game"
            @click="handleGameClick"
          />
        </div>
        
        <div v-if="filteredGames.length === 0" class="no-games">
          <p>没有找到符合条件的游戏</p>
        </div>
        
        <div class="load-more" v-if="hasMore">
          <button class="btn btn-primary" @click="loadMore">
            加载更多
          </button>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import GameCard from '@/components/GameCard.vue'

export default {
  name: 'GamesView',
  components: {
    GameCard
  },
  setup() {
    const activeCategory = ref('all')
    const searchQuery = ref('')
    const hasMore = ref(true)
    
    const categories = ref([
      { id: 'all', name: '全部' },
      { id: 'action', name: '动作' },
      { id: 'adventure', name: '冒险' },
      { id: 'strategy', name: '策略' },
      { id: 'rpg', name: 'RPG' },
      { id: 'racing', name: '竞速' },
      { id: 'simulation', name: '模拟' },
      { id: 'casual', name: '休闲' }
    ])
    
    const allGames = ref([
      {
        id: 1,
        title: '冒险之旅',
        description: '踏上史诗般的冒险旅程，探索神秘的世界',
        image: '/src/assets/images/img1.png',
        category: 'adventure',
        rating: 4.8,
        players: 125000,
        size: '256MB'
      },
      {
        id: 2,
        title: '战略大师',
        description: '运用智慧和策略，建立你的帝国',
        image: '/src/assets/images/img2.png',
        category: 'strategy',
        rating: 4.6,
        players: 89000,
        size: '180MB'
      },
      {
        id: 3,
        title: '竞速狂飙',
        description: '感受速度与激情，成为赛道之王',
        image: '/src/assets/images/img3.png',
        category: 'racing',
        rating: 4.7,
        players: 156000,
        size: '320MB'
      },
      {
        id: 4,
        title: '魔法世界',
        description: '在魔法的世界中展开奇幻冒险',
        image: '/src/assets/images/img4.png',
        category: 'rpg',
        rating: 4.9,
        players: 203000,
        size: '450MB'
      },
      {
        id: 5,
        title: '太空探索',
        description: '探索浩瀚宇宙，发现未知星球',
        image: '/src/assets/images/img5.png',
        category: 'adventure',
        rating: 4.5,
        players: 67000,
        size: '380MB'
      },
      {
        id: 6,
        title: '城市建造',
        description: '建设你的梦想城市，管理城市发展',
        image: '/src/assets/images/img6.png',
        category: 'simulation',
        rating: 4.4,
        players: 94000,
        size: '220MB'
      },
      {
        id: 7,
        title: '武侠传说',
        description: '体验江湖恩怨，成为一代武林高手',
        image: '/src/assets/images/img1.png',
        category: 'rpg',
        rating: 4.6,
        players: 78000,
        size: '340MB'
      },
      {
        id: 8,
        title: '休闲消除',
        description: '轻松愉快的消除游戏，适合所有年龄',
        image: '/src/assets/images/img2.png',
        category: 'casual',
        rating: 4.3,
        players: 234000,
        size: '120MB'
      }
    ])
    
    const filteredGames = computed(() => {
      let games = allGames.value
      
      // 按分类筛选
      if (activeCategory.value !== 'all') {
        games = games.filter(game => game.category === activeCategory.value)
      }
      
      // 按搜索关键词筛选
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase()
        games = games.filter(game => 
          game.title.toLowerCase().includes(query) ||
          game.description.toLowerCase().includes(query)
        )
      }
      
      return games
    })
    
    const setActiveCategory = (categoryId) => {
      activeCategory.value = categoryId
    }
    
    const handleGameClick = (game) => {
      console.log('点击游戏:', game)
      // 这里可以添加游戏详情页面的跳转逻辑
    }
    
    const loadMore = () => {
      // 模拟加载更多游戏
      console.log('加载更多游戏')
      hasMore.value = false
    }
    
    return {
      activeCategory,
      searchQuery,
      hasMore,
      categories,
      filteredGames,
      setActiveCategory,
      handleGameClick,
      loadMore
    }
  }
}
</script>

<style lang="scss" scoped>
.games {
  .page-header {
    background: linear-gradient(135deg, var(--primary-color), darken(#e57c58, 20%));
    color: white;
    padding: 1rem 0 0.8rem;
    text-align: center;
    
    .page-title {
      font-size: 0.4rem;
      font-weight: 700;
      margin-bottom: 0.1rem;
    }
    
    .page-subtitle {
      font-size: 0.16rem;
      opacity: 0.9;
      margin: 0;
    }
  }
  
  .games-filter {
    background: white;
    padding: 0.3rem 0;
    border-bottom: 0.01rem solid #eee;
    
    .container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 0.3rem;
      
      @media (max-width: 768px) {
        flex-direction: column;
        gap: 0.2rem;
      }
    }
    
    .filter-tabs {
      display: flex;
      gap: 0.1rem;
      flex-wrap: wrap;
      
      .filter-tab {
        padding: 0.08rem 0.16rem;
        border: 0.01rem solid #ddd;
        background: white;
        color: #666;
        border-radius: 0.04rem;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.12rem;
        
        &:hover {
          border-color: var(--primary-color);
          color: var(--primary-color);
        }
        
        &.active {
          background: var(--primary-color);
          color: white;
          border-color: var(--primary-color);
        }
      }
    }
    
    .filter-search {
      .search-input {
        padding: 0.08rem 0.12rem;
        border: 0.01rem solid #ddd;
        border-radius: 0.04rem;
        font-size: 0.12rem;
        width: 2rem;
        transition: border-color 0.3s ease;
        
        &:focus {
          outline: none;
          border-color: var(--primary-color);
        }
        
        @media (max-width: 768px) {
          width: 100%;
        }
      }
    }
  }
  
  .games-list {
    padding: 0.6rem 0;
    background: #f8f9fa;
    min-height: 6rem;
    
    .games-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(2.8rem, 1fr));
      gap: 0.3rem;
      margin-bottom: 0.5rem;
      
      @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.2rem;
      }
      
      @media (max-width: 480px) {
        grid-template-columns: 1fr;
      }
    }
    
    .no-games {
      text-align: center;
      padding: 2rem 0;
      
      p {
        font-size: 0.14rem;
        color: #666;
        margin: 0;
      }
    }
    
    .load-more {
      text-align: center;
      margin-top: 0.4rem;
    }
  }
}
</style>
