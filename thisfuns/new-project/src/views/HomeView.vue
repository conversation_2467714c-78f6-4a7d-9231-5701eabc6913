<template>
  <div class="home">
    <!-- Banner轮播 -->
    <BannerSwiper :banners="banners" />
    
    <!-- 游戏推荐区域 -->
    <section class="games-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">热门游戏推荐</h2>
          <p class="section-subtitle">发现最受欢迎的游戏</p>
        </div>
        
        <div class="games-grid">
          <GameCard 
            v-for="game in featuredGames" 
            :key="game.id"
            :game="game"
            @click="handleGameClick"
          />
        </div>
        
        <div class="section-footer">
          <router-link to="/games" class="btn btn-primary btn-large">
            查看更多游戏
          </router-link>
        </div>
      </div>
    </section>
    
    <!-- 关于我们区域 -->
    <section class="about-section">
      <div class="container">
        <div class="about-content">
          <div class="about-text">
            <h2>关于Thisfun</h2>
            <p>
              Thisfun是一个专注于为玩家提供优质游戏体验的平台。我们致力于发现和推广最有趣、
              最具创新性的游戏，让每一位玩家都能在这里找到属于自己的乐趣。
            </p>
            <p>
              无论你是休闲玩家还是硬核玩家，无论你喜欢单机游戏还是多人在线游戏，
              Thisfun都能为你提供丰富多样的选择。
            </p>
            <router-link to="/about" class="btn btn-secondary">
              了解更多
            </router-link>
          </div>
          <div class="about-image">
            <img src="@/assets/images/bg-game.png" alt="游戏世界" />
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ref } from 'vue'
import BannerSwiper from '@/components/BannerSwiper.vue'
import GameCard from '@/components/GameCard.vue'

export default {
  name: 'HomeView',
  components: {
    BannerSwiper,
    GameCard
  },
  setup() {
    const banners = ref([
      {
        image: '/src/assets/images/banner1.png',
        textImage: '/src/assets/images/banner-text.png',
        title: 'Welcome to Thisfun',
        description: '探索无限可能的游戏世界'
      },
      {
        image: '/src/assets/images/banner2.png',
        title: '精彩游戏',
        description: '体验最新最热门的游戏'
      },
      {
        image: '/src/assets/images/banner3.png',
        title: '社区互动',
        description: '与全球玩家一起游戏'
      }
    ])
    
    const featuredGames = ref([
      {
        id: 1,
        title: '冒险之旅',
        description: '踏上史诗般的冒险旅程，探索神秘的世界',
        image: '/src/assets/images/img1.png',
        category: '冒险',
        rating: 4.8,
        players: 125000,
        size: '256MB'
      },
      {
        id: 2,
        title: '战略大师',
        description: '运用智慧和策略，建立你的帝国',
        image: '/src/assets/images/img2.png',
        category: '策略',
        rating: 4.6,
        players: 89000,
        size: '180MB'
      },
      {
        id: 3,
        title: '竞速狂飙',
        description: '感受速度与激情，成为赛道之王',
        image: '/src/assets/images/img3.png',
        category: '竞速',
        rating: 4.7,
        players: 156000,
        size: '320MB'
      },
      {
        id: 4,
        title: '魔法世界',
        description: '在魔法的世界中展开奇幻冒险',
        image: '/src/assets/images/img4.png',
        category: 'RPG',
        rating: 4.9,
        players: 203000,
        size: '450MB'
      },
      {
        id: 5,
        title: '太空探索',
        description: '探索浩瀚宇宙，发现未知星球',
        image: '/src/assets/images/img5.png',
        category: '科幻',
        rating: 4.5,
        players: 67000,
        size: '380MB'
      },
      {
        id: 6,
        title: '城市建造',
        description: '建设你的梦想城市，管理城市发展',
        image: '/src/assets/images/img6.png',
        category: '模拟',
        rating: 4.4,
        players: 94000,
        size: '220MB'
      }
    ])
    
    const handleGameClick = (game) => {
      console.log('点击游戏:', game)
      // 这里可以添加游戏详情页面的跳转逻辑
    }
    
    return {
      banners,
      featuredGames,
      handleGameClick
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  .games-section {
    padding: 0.8rem 0;
    background: #f8f9fa;
    
    .section-header {
      text-align: center;
      margin-bottom: 0.6rem;
      
      .section-title {
        font-size: 0.32rem;
        font-weight: 700;
        color: #333;
        margin-bottom: 0.1rem;
      }
      
      .section-subtitle {
        font-size: 0.14rem;
        color: #666;
        margin: 0;
      }
    }
    
    .games-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(2.8rem, 1fr));
      gap: 0.3rem;
      margin-bottom: 0.5rem;
      
      @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.2rem;
      }
      
      @media (max-width: 480px) {
        grid-template-columns: 1fr;
      }
    }
    
    .section-footer {
      text-align: center;
    }
  }
  
  .about-section {
    padding: 0.8rem 0;
    background: white;
    
    .about-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0.6rem;
      align-items: center;
      
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 0.4rem;
      }
      
      .about-text {
        h2 {
          font-size: 0.28rem;
          font-weight: 700;
          color: #333;
          margin-bottom: 0.2rem;
        }
        
        p {
          font-size: 0.14rem;
          line-height: 1.8;
          color: #666;
          margin-bottom: 0.2rem;
        }
        
        .btn {
          margin-top: 0.2rem;
        }
      }
      
      .about-image {
        text-align: center;
        
        img {
          max-width: 100%;
          height: auto;
          border-radius: 0.08rem;
        }
      }
    }
  }
}
</style>
