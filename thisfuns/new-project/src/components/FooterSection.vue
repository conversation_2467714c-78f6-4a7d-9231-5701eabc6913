<template>
  <footer class="footer">
    <div class="footer-content">
      <div class="footer-section">
        <h3>关于Thisfun</h3>
        <p>Thisfun致力于为玩家提供最优质的游戏体验，打造全球领先的游戏平台。</p>
        <div class="social-links">
          <a href="#" class="social-link">微信</a>
          <a href="#" class="social-link">微博</a>
          <a href="#" class="social-link">QQ</a>
        </div>
      </div>
      
      <div class="footer-section">
        <h3>游戏分类</h3>
        <ul class="footer-links">
          <li><a href="#">动作游戏</a></li>
          <li><a href="#">角色扮演</a></li>
          <li><a href="#">策略游戏</a></li>
          <li><a href="#">休闲游戏</a></li>
        </ul>
      </div>
      
      <div class="footer-section">
        <h3>帮助中心</h3>
        <ul class="footer-links">
          <li><a href="#">常见问题</a></li>
          <li><a href="#">用户协议</a></li>
          <li><a href="#">隐私政策</a></li>
          <li><a href="#">意见反馈</a></li>
        </ul>
      </div>
      
      <div class="footer-section">
        <h3>联系我们</h3>
        <div class="contact-info">
          <div class="contact-item">
            <img src="@/assets/images/email.png" alt="邮箱" class="contact-icon" />
            <span><EMAIL></span>
          </div>
          <div class="contact-item">
            <img src="@/assets/images/address.png" alt="地址" class="contact-icon" />
            <span>北京市朝阳区xxx大厦</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="footer-bottom">
      <p>&copy; 2024 Thisfun. All rights reserved. | 京ICP备xxxxxxxx号</p>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'FooterSection'
}
</script>

<style lang="scss" scoped>
.footer {
  background: #333;
  color: white;
  padding: 0.6rem 0 0.3rem;
  
  .footer-content {
    max-width: 12rem;
    margin: 0 auto;
    padding: 0 0.2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(2.5rem, 1fr));
    gap: 0.4rem;
    
    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 0.3rem;
    }
    
    @media (max-width: 480px) {
      grid-template-columns: 1fr;
    }
  }
  
  .footer-section {
    h3 {
      color: var(--primary-color);
      font-size: 0.16rem;
      margin-bottom: 0.2rem;
      font-weight: 600;
    }
    
    p {
      color: #ccc;
      line-height: 1.6;
      font-size: 0.12rem;
      margin-bottom: 0.15rem;
    }
    
    .footer-links {
      list-style: none;
      padding: 0;
      margin: 0;
      
      li {
        margin-bottom: 0.08rem;
        
        a {
          color: #ccc;
          text-decoration: none;
          font-size: 0.12rem;
          transition: color 0.3s ease;
          
          &:hover {
            color: var(--primary-color);
          }
        }
      }
    }
    
    .social-links {
      display: flex;
      gap: 0.15rem;
      margin-top: 0.15rem;
      
      .social-link {
        display: inline-block;
        padding: 0.08rem 0.12rem;
        background: rgba(255, 255, 255, 0.1);
        color: #ccc;
        text-decoration: none;
        border-radius: 0.04rem;
        font-size: 0.11rem;
        transition: all 0.3s ease;
        
        &:hover {
          background: var(--primary-color);
          color: white;
        }
      }
    }
    
    .contact-info {
      .contact-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.12rem;
        
        .contact-icon {
          width: 0.16rem;
          height: 0.16rem;
          margin-right: 0.08rem;
          filter: brightness(0.8);
        }
        
        span {
          color: #ccc;
          font-size: 0.12rem;
        }
      }
    }
  }
  
  .footer-bottom {
    text-align: center;
    margin-top: 0.4rem;
    padding-top: 0.3rem;
    border-top: 0.01rem solid #555;
    
    p {
      color: #999;
      font-size: 0.11rem;
      margin: 0;
    }
  }
}
</style>
