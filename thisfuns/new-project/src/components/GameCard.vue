<template>
  <div class="game-card" @click="handleClick">
    <div class="card-image-wrapper">
      <img :src="game.image" :alt="game.title" class="card-image" />
      <div class="card-overlay">
        <button class="play-btn">立即游戏</button>
      </div>
    </div>
    
    <div class="card-content">
      <h3 class="card-title">{{ game.title }}</h3>
      <p class="card-description">{{ game.description }}</p>
      
      <div class="card-meta">
        <span class="card-category">{{ game.category }}</span>
        <div class="card-rating">
          <span class="rating-stars">
            <span 
              v-for="star in 5" 
              :key="star"
              class="star"
              :class="{ 'filled': star <= game.rating }"
            >★</span>
          </span>
          <span class="rating-text">{{ game.rating }}/5</span>
        </div>
      </div>
      
      <div class="card-stats">
        <div class="stat-item">
          <span class="stat-label">玩家数</span>
          <span class="stat-value">{{ formatNumber(game.players) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">大小</span>
          <span class="stat-value">{{ game.size }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GameCard',
  props: {
    game: {
      type: Object,
      required: true,
      default: () => ({
        id: 1,
        title: '游戏标题',
        description: '游戏描述',
        image: '/src/assets/images/img1.png',
        category: '动作',
        rating: 4.5,
        players: 10000,
        size: '100MB'
      })
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const handleClick = () => {
      emit('click', props.game)
    }
    
    const formatNumber = (num) => {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K'
      }
      return num.toString()
    }
    
    return {
      handleClick,
      formatNumber
    }
  }
}
</script>

<style lang="scss" scoped>
.game-card {
  background: white;
  border-radius: 0.08rem;
  box-shadow: 0 0.02rem 0.08rem rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:hover {
    transform: translateY(-0.04rem);
    box-shadow: 0 0.08rem 0.24rem rgba(0, 0, 0, 0.15);
    
    .card-overlay {
      opacity: 1;
    }
  }
  
  .card-image-wrapper {
    position: relative;
    width: 100%;
    height: 2rem;
    overflow: hidden;
    
    .card-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
    
    .card-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      
      .play-btn {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 0.1rem 0.2rem;
        border-radius: 0.04rem;
        font-size: 0.12rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          background: darken(#e57c58, 10%);
          transform: scale(1.05);
        }
      }
    }
  }
  
  .card-content {
    padding: 0.15rem;
    
    .card-title {
      font-size: 0.14rem;
      font-weight: 600;
      color: #333;
      margin: 0 0 0.08rem 0;
      line-height: 1.4;
    }
    
    .card-description {
      font-size: 0.11rem;
      color: #666;
      line-height: 1.5;
      margin: 0 0 0.12rem 0;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    
    .card-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.12rem;
      
      .card-category {
        background: rgba(229, 124, 88, 0.1);
        color: var(--primary-color);
        padding: 0.02rem 0.08rem;
        border-radius: 0.02rem;
        font-size: 0.1rem;
        font-weight: 500;
      }
      
      .card-rating {
        display: flex;
        align-items: center;
        gap: 0.04rem;
        
        .rating-stars {
          .star {
            color: #ddd;
            font-size: 0.1rem;
            
            &.filled {
              color: #ffc107;
            }
          }
        }
        
        .rating-text {
          font-size: 0.1rem;
          color: #666;
        }
      }
    }
    
    .card-stats {
      display: flex;
      justify-content: space-between;
      
      .stat-item {
        text-align: center;
        
        .stat-label {
          display: block;
          font-size: 0.09rem;
          color: #999;
          margin-bottom: 0.02rem;
        }
        
        .stat-value {
          display: block;
          font-size: 0.11rem;
          font-weight: 600;
          color: #333;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .game-card {
    .card-image-wrapper {
      height: 1.6rem;
    }
    
    .card-content {
      padding: 0.12rem;
      
      .card-title {
        font-size: 0.13rem;
      }
      
      .card-description {
        font-size: 0.1rem;
      }
    }
  }
}
</style>
