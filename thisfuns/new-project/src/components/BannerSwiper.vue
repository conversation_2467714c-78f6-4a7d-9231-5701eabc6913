<template>
  <div class="banner-swiper">
    <div class="swiper-container" ref="swiperContainer">
      <div class="swiper-wrapper">
        <div 
          v-for="(banner, index) in banners" 
          :key="index"
          class="swiper-slide"
          :style="{ backgroundImage: `url(${banner.image})` }"
        >
          <div class="banner-content">
            <img 
              v-if="banner.textImage" 
              :src="banner.textImage" 
              alt="Banner Text" 
              class="banner-text"
            />
            <h1 v-else class="banner-title">{{ banner.title }}</h1>
            <p v-if="banner.description" class="banner-description">
              {{ banner.description }}
            </p>
          </div>
        </div>
      </div>
      
      <!-- 分页器 -->
      <div class="swiper-pagination"></div>
      
      <!-- 导航按钮 -->
      <div class="swiper-button-next"></div>
      <div class="swiper-button-prev"></div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'BannerSwiper',
  props: {
    banners: {
      type: Array,
      default: () => [
        {
          image: '/src/assets/images/banner1.png',
          textImage: '/src/assets/images/banner-text.png',
          title: 'Welcome to Thisfun',
          description: '探索无限可能的游戏世界'
        },
        {
          image: '/src/assets/images/banner2.png',
          title: '精彩游戏',
          description: '体验最新最热门的游戏'
        },
        {
          image: '/src/assets/images/banner3.png',
          title: '社区互动',
          description: '与全球玩家一起游戏'
        }
      ]
    },
    autoplay: {
      type: Boolean,
      default: true
    },
    delay: {
      type: Number,
      default: 5000
    }
  },
  setup(props) {
    const swiperContainer = ref(null)
    let swiper = null
    
    const initSwiper = () => {
      // 简单的轮播实现
      if (!swiperContainer.value) return
      
      const slides = swiperContainer.value.querySelectorAll('.swiper-slide')
      const pagination = swiperContainer.value.querySelector('.swiper-pagination')
      let currentSlide = 0
      let autoplayTimer = null
      
      // 创建分页器
      if (pagination) {
        pagination.innerHTML = ''
        slides.forEach((_, index) => {
          const bullet = document.createElement('span')
          bullet.className = `swiper-pagination-bullet ${index === 0 ? 'swiper-pagination-bullet-active' : ''}`
          bullet.addEventListener('click', () => goToSlide(index))
          pagination.appendChild(bullet)
        })
      }
      
      const updateSlides = () => {
        slides.forEach((slide, index) => {
          slide.style.transform = `translateX(${(index - currentSlide) * 100}%)`
        })
        
        // 更新分页器
        const bullets = pagination?.querySelectorAll('.swiper-pagination-bullet')
        bullets?.forEach((bullet, index) => {
          bullet.classList.toggle('swiper-pagination-bullet-active', index === currentSlide)
        })
      }
      
      const goToSlide = (index) => {
        currentSlide = index
        updateSlides()
        resetAutoplay()
      }
      
      const nextSlide = () => {
        currentSlide = (currentSlide + 1) % slides.length
        updateSlides()
      }
      
      const prevSlide = () => {
        currentSlide = (currentSlide - 1 + slides.length) % slides.length
        updateSlides()
      }
      
      const startAutoplay = () => {
        if (props.autoplay) {
          autoplayTimer = setInterval(nextSlide, props.delay)
        }
      }
      
      const resetAutoplay = () => {
        if (autoplayTimer) {
          clearInterval(autoplayTimer)
          startAutoplay()
        }
      }
      
      // 导航按钮事件
      const nextBtn = swiperContainer.value.querySelector('.swiper-button-next')
      const prevBtn = swiperContainer.value.querySelector('.swiper-button-prev')
      
      nextBtn?.addEventListener('click', () => {
        nextSlide()
        resetAutoplay()
      })
      
      prevBtn?.addEventListener('click', () => {
        prevSlide()
        resetAutoplay()
      })
      
      // 初始化
      updateSlides()
      startAutoplay()
      
      // 返回清理函数
      return () => {
        if (autoplayTimer) {
          clearInterval(autoplayTimer)
        }
      }
    }
    
    onMounted(() => {
      swiper = initSwiper()
    })
    
    onUnmounted(() => {
      if (swiper) {
        swiper()
      }
    })
    
    return {
      swiperContainer
    }
  }
}
</script>

<style lang="scss" scoped>
.banner-swiper {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  
  .swiper-container {
    position: relative;
    width: 100%;
    height: 100%;
  }
  
  .swiper-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
  }
  
  .swiper-slide {
    position: absolute;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    transition: transform 0.5s ease;
    
    .banner-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: white;
      z-index: 2;
      
      .banner-text {
        max-width: 3rem;
        height: auto;
      }
      
      .banner-title {
        font-size: 0.48rem;
        font-weight: bold;
        margin-bottom: 0.2rem;
        text-shadow: 0.02rem 0.02rem 0.04rem rgba(0, 0, 0, 0.5);
      }
      
      .banner-description {
        font-size: 0.18rem;
        margin-bottom: 0.3rem;
        text-shadow: 0.01rem 0.01rem 0.02rem rgba(0, 0, 0, 0.5);
      }
    }
  }
  
  .swiper-pagination {
    position: absolute;
    bottom: 0.3rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.1rem;
    z-index: 10;
    
    .swiper-pagination-bullet {
      width: 0.12rem;
      height: 0.12rem;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.5);
      cursor: pointer;
      transition: all 0.3s ease;
      
      &.swiper-pagination-bullet-active {
        background: var(--primary-color);
        transform: scale(1.2);
      }
    }
  }
  
  .swiper-button-next,
  .swiper-button-prev {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 0.5rem;
    height: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    border: 0.02rem solid rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
    
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 0.12rem;
      height: 0.12rem;
      border-top: 0.02rem solid white;
      border-right: 0.02rem solid white;
    }
  }
  
  .swiper-button-next {
    right: 0.3rem;
    
    &::after {
      transform: translate(-60%, -50%) rotate(45deg);
    }
  }
  
  .swiper-button-prev {
    left: 0.3rem;
    
    &::after {
      transform: translate(-40%, -50%) rotate(-135deg);
    }
  }
  
  @media (max-width: 768px) {
    .swiper-slide .banner-content {
      .banner-text {
        max-width: 2.5rem;
      }
      
      .banner-title {
        font-size: 0.32rem;
      }
      
      .banner-description {
        font-size: 0.14rem;
      }
    }
    
    .swiper-button-next,
    .swiper-button-prev {
      width: 0.4rem;
      height: 0.4rem;
    }
  }
}
</style>
