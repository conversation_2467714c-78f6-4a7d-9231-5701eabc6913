<template>
  <nav class="navbar" :class="{ 'scrolled': isScrolled }">
    <div class="nav-container">
      <!-- Logo -->
      <router-link to="/" class="nav-logo-link">
        <img src="@/assets/images/company.png" alt="Thisfun" class="nav-logo" />
      </router-link>
      
      <!-- 导航菜单 -->
      <ul class="nav-menu" :class="{ 'active': isMenuOpen }">
        <li class="nav-item">
          <router-link to="/" class="nav-link" @click="closeMenu">首页</router-link>
        </li>
        <li class="nav-item">
          <router-link to="/about" class="nav-link" @click="closeMenu">关于我们</router-link>
        </li>
        <li class="nav-item">
          <router-link to="/games" class="nav-link" @click="closeMenu">游戏中心</router-link>
        </li>
        <li class="nav-item">
          <router-link to="/contact" class="nav-link" @click="closeMenu">联系我们</router-link>
        </li>
      </ul>
      
      <!-- 移动端菜单按钮 -->
      <button class="nav-toggle" @click="toggleMenu">
        <span class="hamburger"></span>
        <span class="hamburger"></span>
        <span class="hamburger"></span>
      </button>
    </div>
  </nav>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'NavBar',
  setup() {
    const isScrolled = ref(false)
    const isMenuOpen = ref(false)
    
    const handleScroll = () => {
      isScrolled.value = window.scrollY > 50
    }
    
    const toggleMenu = () => {
      isMenuOpen.value = !isMenuOpen.value
    }
    
    const closeMenu = () => {
      isMenuOpen.value = false
    }
    
    onMounted(() => {
      window.addEventListener('scroll', handleScroll)
    })
    
    onUnmounted(() => {
      window.removeEventListener('scroll', handleScroll)
    })
    
    return {
      isScrolled,
      isMenuOpen,
      toggleMenu,
      closeMenu
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(0.1rem);
  z-index: 1000;
  padding: 0.15rem 0;
  transition: all 0.3s ease;
  
  &.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 0.02rem 0.1rem rgba(0, 0, 0, 0.1);
  }
  
  .nav-container {
    max-width: 12rem;
    margin: 0 auto;
    padding: 0 0.2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .nav-logo {
    height: 0.4rem;
    width: auto;
  }
  
  .nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    
    .nav-item {
      margin-left: 0.3rem;
      
      .nav-link {
        color: #333;
        text-decoration: none;
        font-size: 0.14rem;
        font-weight: 500;
        transition: color 0.3s ease;
        padding: 0.1rem 0.15rem;
        border-radius: 0.04rem;
        
        &:hover,
        &.router-link-active {
          color: var(--primary-color);
        }
      }
    }
  }
  
  .nav-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.05rem;
    
    .hamburger {
      width: 0.25rem;
      height: 0.02rem;
      background: #333;
      margin: 0.02rem 0;
      transition: 0.3s;
      border-radius: 0.01rem;
    }
    
    @media (max-width: 768px) {
      display: flex;
    }
  }
  
  @media (max-width: 768px) {
    .nav-menu {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      flex-direction: column;
      box-shadow: 0 0.04rem 0.16rem rgba(0, 0, 0, 0.1);
      transform: translateY(-100%);
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
      
      &.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
      }
      
      .nav-item {
        margin: 0;
        border-bottom: 0.01rem solid #eee;
        
        &:last-child {
          border-bottom: none;
        }
        
        .nav-link {
          display: block;
          padding: 0.15rem 0.2rem;
          font-size: 0.16rem;
        }
      }
    }
  }
}
</style>
