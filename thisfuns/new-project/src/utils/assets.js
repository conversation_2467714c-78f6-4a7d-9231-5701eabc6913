// 资源文件管理工具
// 统一管理所有静态资源的引用

// 图片资源映射
export const images = {
  // Banner相关
  banner1: () => import('@/assets/images/banner1.png'),
  banner2: () => import('@/assets/images/banner2.png'),
  banner3: () => import('@/assets/images/banner3.png'),
  bannerText: () => import('@/assets/images/banner-text.png'),
  
  // 背景图片
  bgGame: () => import('@/assets/images/bg-game.png'),
  bgContact: () => import('@/assets/images/bg-contact.jpeg'),
  
  // 内容图片
  img1: () => import('@/assets/images/img1.png'),
  img2: () => import('@/assets/images/img2.png'),
  img3: () => import('@/assets/images/img3.png'),
  img4: () => import('@/assets/images/img4.png'),
  img5: () => import('@/assets/images/img5.png'),
  img6: () => import('@/assets/images/img6.png'),
  
  // 图标
  company: () => import('@/assets/images/company.png'),
  email: () => import('@/assets/images/email.png'),
  address: () => import('@/assets/images/address.png'),
  reply: () => import('@/assets/images/reply.png'),
  
  // 其他
  messageImg: () => import('@/assets/images/message-img.jpeg')
}

// 获取图片URL的辅助函数
export const getImageUrl = async (imageName) => {
  if (images[imageName]) {
    const module = await images[imageName]()
    return module.default
  }
  console.warn(`Image ${imageName} not found`)
  return ''
}

// 同步获取图片URL（用于已知的静态路径）
export const getStaticImageUrl = (imageName) => {
  const imageMap = {
    banner1: '/src/assets/images/banner1.png',
    banner2: '/src/assets/images/banner2.png',
    banner3: '/src/assets/images/banner3.png',
    bannerText: '/src/assets/images/banner-text.png',
    bgGame: '/src/assets/images/bg-game.png',
    bgContact: '/src/assets/images/bg-contact.jpeg',
    img1: '/src/assets/images/img1.png',
    img2: '/src/assets/images/img2.png',
    img3: '/src/assets/images/img3.png',
    img4: '/src/assets/images/img4.png',
    img5: '/src/assets/images/img5.png',
    img6: '/src/assets/images/img6.png',
    company: '/src/assets/images/company.png',
    email: '/src/assets/images/email.png',
    address: '/src/assets/images/address.png',
    reply: '/src/assets/images/reply.png',
    messageImg: '/src/assets/images/message-img.jpeg'
  }
  
  return imageMap[imageName] || ''
}
