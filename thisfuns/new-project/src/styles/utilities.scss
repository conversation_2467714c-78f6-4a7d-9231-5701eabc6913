// 工具类样式 - 基于原项目的rem系统生成

// 生成间距工具类 (基于100px的rem系统)
@for $i from 1 through 200 {
  .pl#{$i} { padding-left: #{$i * 0.01}rem; }
  .pr#{$i} { padding-right: #{$i * 0.01}rem; }
  .pt#{$i} { padding-top: #{$i * 0.01}rem; }
  .pb#{$i} { padding-bottom: #{$i * 0.01}rem; }
  .p#{$i} { padding: #{$i * 0.01}rem; }
  
  .ml#{$i} { margin-left: #{$i * 0.01}rem; }
  .mr#{$i} { margin-right: #{$i * 0.01}rem; }
  .mt#{$i} { margin-top: #{$i * 0.01}rem; }
  .mb#{$i} { margin-bottom: #{$i * 0.01}rem; }
  .m#{$i} { margin: #{$i * 0.01}rem; }
}

// 生成宽高工具类
@for $i from 1 through 200 {
  .w#{$i} { width: #{$i * 0.01}rem; }
  .h#{$i} { height: #{$i * 0.01}rem; }
}

// 生成字体大小工具类
@for $i from 8 through 50 {
  .fz#{$i} { font-size: #{$i * 0.01}rem; }
}

// 生成行高工具类
@for $i from 1 through 50 {
  .lh#{$i} { line-height: #{$i * 0.01}rem; }
}

// 生成圆角工具类
@for $i from 1 through 50 {
  .rad#{$i} { border-radius: #{$i * 0.01}rem; }
}

// 生成定位工具类
@for $i from 1 through 100 {
  .top#{$i} { top: #{$i * 0.01}rem; }
  .bottom#{$i} { bottom: #{$i * 0.01}rem; }
  .left#{$i} { left: #{$i * 0.01}rem; }
  .right#{$i} { right: #{$i * 0.01}rem; }
}

// 特殊工具类
.pb180 { padding-bottom: 1.8rem; }
.mt160 { margin-top: 1.6rem; }

// 轮播图相关
.mv__item {
  padding-bottom: 51%;
  overflow: hidden;
  position: relative;
  
  img {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

// 布局工具类
.container {
  max-width: 12rem;
  margin: 0 auto;
  padding: 0 $spacing-md;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -#{$spacing-sm};
}

.col {
  flex: 1;
  padding: 0 $spacing-sm;
}

// 响应式列
@for $i from 1 through 12 {
  .col-#{$i} {
    flex: 0 0 percentage($i / 12);
    max-width: percentage($i / 12);
    padding: 0 $spacing-sm;
  }
}

// 响应式显示/隐藏
@media (max-width: $breakpoint-mobile) {
  .hidden-mobile {
    display: none !important;
  }
}

@media (min-width: $breakpoint-mobile + 1) and (max-width: $breakpoint-tablet) {
  .hidden-tablet {
    display: none !important;
  }
}

@media (min-width: $breakpoint-tablet + 1) {
  .hidden-desktop {
    display: none !important;
  }
}

// 文本对齐
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

// 浮动
.float-left { float: left; }
.float-right { float: right; }
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

// 定位
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

// 显示
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.grid { display: grid; }
.hidden { display: none; }

// Flex工具类
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

// 溢出
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

// 宽高百分比
.w-full { width: 100%; }
.h-full { height: 100%; }
.w-auto { width: auto; }
.h-auto { height: auto; }

// 最大最小宽高
.max-w-full { max-width: 100%; }
.max-h-full { max-height: 100%; }
.min-w-0 { min-width: 0; }
.min-h-0 { min-height: 0; }

// 字体权重
.font-thin { font-weight: 100; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

// 颜色工具类
.text-primary { color: $primary-color; }
.text-white { color: white; }
.text-black { color: black; }
.text-gray { color: #666; }
.text-light-gray { color: #999; }

.bg-primary { background-color: $primary-color; }
.bg-white { background-color: white; }
.bg-black { background-color: black; }
.bg-gray { background-color: #f5f5f5; }
.bg-transparent { background-color: transparent; }
