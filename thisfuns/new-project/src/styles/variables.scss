// 设计系统变量定义

// 颜色系统
:root {
  --primary-color: #e57c58;
  --text-color: #333;
  --bg-color: #fff;
  --border-color: #ddd;
}

// 主色调
$primary-color: #e57c58;
$text-color: #333;
$bg-color: #fff;
$border-color: #ddd;

// 字体系统
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-family-alegreya: 'AlegreyaSans', $font-family-base;

// 字体大小 (基于100px的rem系统)
$font-size-base: 0.12rem; // 12px
$font-size-sm: 0.10rem;   // 10px
$font-size-lg: 0.14rem;   // 14px
$font-size-xl: 0.16rem;   // 16px
$font-size-xxl: 0.18rem;  // 18px

// 间距系统 (基于100px的rem系统)
$spacing-xs: 0.05rem;   // 5px
$spacing-sm: 0.10rem;   // 10px
$spacing-md: 0.15rem;   // 15px
$spacing-lg: 0.20rem;   // 20px
$spacing-xl: 0.30rem;   // 30px
$spacing-xxl: 0.50rem;  // 50px

// 断点系统
$breakpoint-mobile: 480px;
$breakpoint-tablet: 768px;
$breakpoint-desktop: 1024px;

// 层级系统
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// 动画
$transition-base: all 0.3s ease;
$transition-fast: all 0.15s ease;
$transition-slow: all 0.5s ease;

// 边框圆角
$border-radius-sm: 0.02rem;  // 2px
$border-radius-md: 0.04rem;  // 4px
$border-radius-lg: 0.08rem;  // 8px

// 阴影
$box-shadow-sm: 0 0.01rem 0.03rem rgba(0, 0, 0, 0.12);
$box-shadow-md: 0 0.02rem 0.08rem rgba(0, 0, 0, 0.15);
$box-shadow-lg: 0 0.04rem 0.16rem rgba(0, 0, 0, 0.18);
