// 组件样式库

// Banner轮播组件样式
.banner-swiper {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  
  .swiper-slide {
    position: relative;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .banner-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 2;
    
    .banner-text {
      max-width: 3rem;
      margin: 0 auto;
    }
  }
  
  // 分页器样式
  .swiper-pagination {
    bottom: 0.2rem;
    
    .swiper-pagination-bullet {
      width: 0.08rem;
      height: 0.08rem;
      background: rgba(255, 255, 255, 0.5);
      opacity: 1;
      
      &.swiper-pagination-bullet-active {
        background: $primary-color;
      }
    }
  }
}

// 内容卡片组件
.content-card {
  background: white;
  border-radius: $border-radius-md;
  box-shadow: $box-shadow-sm;
  padding: $spacing-lg;
  margin-bottom: $spacing-md;
  transition: $transition-base;
  
  &:hover {
    box-shadow: $box-shadow-md;
    transform: translateY(-0.02rem);
  }
  
  .card-image {
    width: 100%;
    height: 2rem;
    object-fit: cover;
    border-radius: $border-radius-sm;
    margin-bottom: $spacing-md;
  }
  
  .card-title {
    font-size: $font-size-lg;
    font-weight: bold;
    color: $text-color;
    margin-bottom: $spacing-sm;
  }
  
  .card-description {
    font-size: $font-size-base;
    color: lighten($text-color, 20%);
    line-height: 1.6;
  }
}

// 按钮组件
.btn {
  display: inline-block;
  padding: $spacing-sm $spacing-lg;
  border: none;
  border-radius: $border-radius-sm;
  font-size: $font-size-base;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: $transition-fast;
  
  &.btn-primary {
    background: $primary-color;
    color: white;
    
    &:hover {
      background: darken($primary-color, 10%);
    }
  }
  
  &.btn-secondary {
    background: transparent;
    color: $primary-color;
    border: 0.01rem solid $primary-color;
    
    &:hover {
      background: $primary-color;
      color: white;
    }
  }
  
  &.btn-large {
    padding: $spacing-md $spacing-xl;
    font-size: $font-size-lg;
  }
  
  &.btn-small {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
  }
}

// 导航组件
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(0.1rem);
  z-index: $z-index-fixed;
  padding: $spacing-sm 0;
  transition: $transition-base;
  
  .nav-container {
    max-width: 12rem;
    margin: 0 auto;
    padding: 0 $spacing-md;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .nav-logo {
    height: 0.4rem;
  }
  
  .nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    
    .nav-item {
      margin-left: $spacing-lg;
      
      .nav-link {
        color: $text-color;
        text-decoration: none;
        font-size: $font-size-base;
        transition: $transition-fast;
        
        &:hover,
        &.active {
          color: $primary-color;
        }
      }
    }
  }
  
  .nav-toggle {
    display: none;
    background: none;
    border: none;
    font-size: $font-size-lg;
    cursor: pointer;
    
    @media (max-width: $breakpoint-tablet) {
      display: block;
    }
  }
  
  @media (max-width: $breakpoint-tablet) {
    .nav-menu {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      flex-direction: column;
      box-shadow: $box-shadow-md;
      transform: translateY(-100%);
      opacity: 0;
      visibility: hidden;
      transition: $transition-base;
      
      &.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
      }
      
      .nav-item {
        margin: 0;
        border-bottom: 0.01rem solid $border-color;
        
        .nav-link {
          display: block;
          padding: $spacing-md;
        }
      }
    }
  }
}

// 联系表单组件
.contact-form {
  background: white;
  padding: $spacing-xl;
  border-radius: $border-radius-lg;
  box-shadow: $box-shadow-md;
  
  .form-group {
    margin-bottom: $spacing-lg;
    
    label {
      display: block;
      margin-bottom: $spacing-xs;
      font-weight: bold;
      color: $text-color;
    }
    
    input,
    textarea {
      width: 100%;
      padding: $spacing-sm;
      border: 0.01rem solid $border-color;
      border-radius: $border-radius-sm;
      font-size: $font-size-base;
      transition: $transition-fast;
      
      &:focus {
        border-color: $primary-color;
        box-shadow: 0 0 0 0.02rem rgba($primary-color, 0.2);
      }
    }
    
    textarea {
      min-height: 1.2rem;
      resize: vertical;
    }
  }
}

// 页脚组件
.footer {
  background: #333;
  color: white;
  padding: $spacing-xl 0;
  
  .footer-content {
    max-width: 12rem;
    margin: 0 auto;
    padding: 0 $spacing-md;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(2.5rem, 1fr));
    gap: $spacing-lg;
  }
  
  .footer-section {
    h3 {
      margin-bottom: $spacing-md;
      color: $primary-color;
    }
    
    p, a {
      color: #ccc;
      text-decoration: none;
      line-height: 1.6;
      
      &:hover {
        color: white;
      }
    }
  }
  
  .footer-bottom {
    text-align: center;
    margin-top: $spacing-xl;
    padding-top: $spacing-lg;
    border-top: 0.01rem solid #555;
    color: #999;
  }
}
