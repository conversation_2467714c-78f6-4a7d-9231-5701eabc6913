// 基础样式重置和全局样式

// 字体引入
@font-face {
  font-family: 'AlegreyaSans';
  src: url('@/assets/fonts/AlegreyaSans-Black-2.aea01002.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'AlegreyaSans';
  src: url('@/assets/fonts/AlegreyaSans-BlackItalic-3.24178e70.ttf') format('truetype');
  font-weight: 900;
  font-style: italic;
}

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none;
}

html {
  // 设置根字体大小为100px，便于rem计算
  font-size: 100px;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  -webkit-overflow-scrolling: touch;
}

body {
  margin: 0;
  font-size: $font-size-base;
  line-height: 2;
  color: $text-color;
  background-color: $bg-color;
  font-family: $font-family-base;
}

// 链接样式
a {
  background: transparent;
  text-decoration: none;
  color: #08c;
  transition: $transition-fast;
  
  &:hover {
    color: #069;
  }
  
  &:active,
  &:hover {
    outline: 0;
  }
}

// 图片样式
img {
  border: 0;
  vertical-align: middle;
  max-width: 100%;
  height: auto;
}

// 列表样式
ul, ol {
  list-style: none;
}

// 表格样式
table {
  border-collapse: collapse;
  border-spacing: 0;
}

// 表单元素样式
button, input, optgroup, select, textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}

button {
  overflow: visible;
  cursor: pointer;
  border: none;
  background: none;
  
  &:focus {
    outline: none;
  }
}

input, textarea {
  &:focus {
    outline: none;
  }
}

// 工具类
.clearfix {
  &::after {
    content: "";
    display: table;
    clear: both;
  }
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.float-left {
  float: left;
}

.float-right {
  float: right;
}

.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.position-fixed {
  position: fixed;
}

.overflow-hidden {
  overflow: hidden;
}

.display-none {
  display: none;
}

.display-block {
  display: block;
}

.display-inline-block {
  display: inline-block;
}

.width-full {
  width: 100%;
}

.height-full {
  height: 100%;
}

// 响应式工具类
@media (max-width: $breakpoint-mobile) {
  .mobile-hidden {
    display: none !important;
  }
}

@media (min-width: $breakpoint-mobile + 1) {
  .mobile-only {
    display: none !important;
  }
}

// 加载动画
.loader {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  
  &::after {
    content: "";
    display: block;
    width: 0.5rem;
    height: 0.5rem;
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -0.25rem 0 0 -0.25rem;
    border: 0.01rem solid rgba(0, 0, 0, 0.08);
    border-left-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    animation: spinner 0.7s linear infinite;
  }
}

@keyframes spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
